import Facebook from "../../../assets/images/Analytics/facebook.svg";
import PinterestLogo from "../../../assets/images/Analytics/pinterest.svg";
import VimoLogo from "../../../assets/images/Analytics/vimeo.svg";
import LinkedInLogo from "../../../assets/images/Analytics/linkedIn.svg";
import Twitter<PERSON>ogo from "../../../assets/images/Analytics/X.svg";
import TiktokLogo from "../../../assets/images/Analytics/tiktok.svg";
import RedditLogo from "../../../assets/images/Analytics/reddit.svg";
import ThreadsLogo from "../../../assets/images/Analytics/thread.svg";
import InstagramLogo from "../../../assets/images/Analytics/instagram.svg";
import YoutubeLogo from "../../../assets/images/Analytics/youtube.svg";
import TumblrLogo from "../../../assets/images/Analytics/tumblr.svg";
import XLogo from "../../../assets/images/Analytics/X.svg";
import Telegram from "../../../assets/images/Analytics/telegram.svg";
import Mastodon from "../../../assets/images/Analytics/mastodon.svg";
import FlowkarLogo from "../../../assets/images/Chating/flowkarChat.svg";
import { Dialog } from "@mui/material";
import { IntlContext } from "../../../App";
import { useContext } from "react";

export default function ConnectDialog({ open, onClose, platform, onConfirm }) {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  if (!open || !platform) return null;

  const config = platformConfig[platform];

  return (
    <Dialog open={open} onClose={onClose}>
      <div
        className="fixed inset-0 bg-black/50 flex items-center justify-center px-4 font-Ubuntu"
        onClick={onClose}
      >
        <div
          className="bg-white rounded-2xl shadow-xl w-full max-w-sm p-6 relative"
          onClick={(e) => e.stopPropagation()}
        >
          <button
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
            onClick={onClose}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <div className="flex flex-col items-start justify-center text-center">
            <div className="w-[45px] h-[45px] rounded-full flex items-center justify-center mb-4">
              {config?.icon && (
                <img
                  src={config.icon}
                  alt={platform}
                  className="w-[45px] h-[45px]"
                />
              )}
            </div>

            <h2 className="text-[18px] font-bold text-[#000000] mb-1">
              {config?.title ||
                localesData?.USER_WEB?.BRANDS?.SHARE_VIA_CONNECTED}
            </h2>

            <div
              className="text-[18px] font-bold mb-1 capitalize mt-[15px]"
              style={{ color: config?.color }}
            >
              {platform}
            </div>

            <p className="text-[#A9ABAD] text-[14px] font-normal leading-[20px] max-w-xs text-left">
              {config?.message || localesData?.USER_WEB?.BRANDS?.POPUP_TEXT}
            </p>
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <button
              onClick={onClose}
              className="px-5 py-2 text-sm border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-100"
            >
              {config?.cancelText || localesData?.USER_WEB?.BRANDS?.CANCEL}
            </button>
            <button
              onClick={onConfirm}
              className="px-5 py-2 text-sm bg-[#4B2C27] text-white rounded-lg hover:bg-[#3c221f]"
            >
              {config?.confirmText || localesData?.USER_WEB?.BRANDS?.CONFIRM}
            </button>
          </div>
        </div>
      </div>
    </Dialog>
  );
}

const platformConfig = {
  facebook: {
    color: "#0866FF",
    icon: Facebook,
    title: "Connect to Facebook",
    message:
      "To connect Facebook, your account must be linked to at least one Facebook Page; otherwise, authentication will not be successful. Additionally, only one Page can be connected per brand—if multiple Pages are selected, the first one will be connected automatically.",
    confirmText: "Connect",
    cancelText: "Cancel",
  },
  pinterest: {
    color: "#E60023",
    icon: PinterestLogo,
    title: "Connect to Pinterest",
    message: " Connect Your pinterest account to share your profile.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  vimeo: {
    color: "#1EB8EB",
    icon: VimoLogo,
    title: "Connect to Vimeo",
    message: "Connect Your vimeo account to share your profile.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  linkedin: {
    color: "#0A66C2",
    icon: LinkedInLogo,
    title: "Connect to LinkedIn",
    message: "Connect Your linkedin account to share your profile.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  twitter: {
    color: "#100E0F",
    icon: TwitterLogo,
    title: "Connect to X (Twitter)",
    message:
      "Join conversations and share quick updates. Connect your X account to engage with your community.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  tiktok: {
    color: "#000000",
    icon: TiktokLogo,
    title: "Connect to TikTok",
    message:
      "To connect and upload content on TikTok, your device must be located in a country where access is unrestricted; otherwise, authentication will fail.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  reddit: {
    color: "#FC471E",
    icon: RedditLogo,
    title: "Connect to Reddit",
    message:
      "Engage with communities and share in relevant subreddits. Connect to participate in discussions.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  threads: {
    color: "#000000",
    icon: ThreadsLogo,
    title: "Connect to Threads",
    message:
      "To connect Threads, your account must be a Business or Professional account; otherwise, authentication will fail.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  thread: {
    color: "#000000",
    icon: ThreadsLogo,
    title: "Connect to Threads",
    message:
      "To connect Threads, your account must be a Business or Professional account; otherwise, authentication will fail.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  instagram: {
    color: "#C30FB2",
    icon: InstagramLogo,
    title: "Connect to Instagram",
    message:
      "To connect Instagram, your account must be set to a Business or Professional type; otherwise, authentication will not be successful. Additionally, if you want to use messaging and analytics features, your Instagram account must be linked to a Facebook.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  youtube: {
    color: "#FF0302",
    icon: YoutubeLogo,
    title: "Connect to YouTube",
    message:
      "To connect YouTube, your account must have an active channel; otherwise, authentication will not be successful.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  tumblr: {
    color: "#35465C",
    icon: TumblrLogo,
    title: "Connect to Tumblr",
    message:
      "Express yourself through multimedia posts and connect with creative communities.",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  x: {
    color: "#35465C",
    icon: XLogo,
    title: "Connect to X",
    message: "Connect Your X account & enjoy the premium Experience of Flowkar",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  telegram: {
    color: "#0088cc",
    icon: Telegram,
    title: "Connect to Telegram",
    message:
      "Connect Your Telegram account & enjoy the premium Experience of Flowkar",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  mastodon: {
    color: "#6364FF",
    icon: Mastodon,
    title: "Connect to Mastodon",
    message:
      "Connect Your Mastodon account & enjoy the premium Experience of Flowkar",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
  flowkar: {
    color: "#563D39",
    icon: FlowkarLogo,
    title: "Connect to Flowkar",
    message:
      "Connect Your Flowkar account & enjoy the premium Experience of Flowkar",
    confirmText: "Connect ",
    cancelText: "Cancel",
  },
};

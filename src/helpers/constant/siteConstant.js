import Instagram from "../../assets/images/svg_icon/instagram-icon.svg";
import Linkedin from "../../assets/images/svg_icon/linkdin-icon.svg";
import Pinterest from "../../assets/images/svg_icon/pintrest-icon.svg";
import Facebook from "../../assets/images/svg_icon/facebook-icon.svg";
import TikTok from "../../assets/images/svg_icon/tiktok-icon.svg";
import Twitter from "../../assets/images/svg_icon/twitter-icon.svg";
import Vimeo from "../../assets/images/svg_icon/vimeo-icon.svg";
import YouTube from "../../assets/images/svg_icon/youtube-icon.svg";
import Audience from "../../assets/images/svg_icon/audience-icon.svg";
import Calender from "../../assets/images/svg_icon/calender-icon.svg";
import Location from "../../assets/images/svg_icon/location-icon.svg";
import Person from "../../assets/images/svg_icon/person-icon.svg";
import Dailymation from "../../assets/images/svg_icon/dailymotion-icon.svg";
import logo from "../../assets/images/flowkar-logo.svg";
import dummyProfile from "../../assets/images/svg_icon/dummy_profile.svg";
import deletePostConfirmation from "../../assets/images/svg_icon/delete-post-confirmation.svg";
import deleteUserInfo from "../../assets/images/svg_icon/info-icon.svg";
import deleteUserBGImage from "../../assets/images/svg_icon/delete-icon-bg.svg";
import DashboardIcon from "../../assets/images/svg_icon/dashboard-icon.svg";
import DashboardIconActive from "../../assets/images/svg_icon/dashboard-icon-active.svg";
import AddIcon from "../../assets/images/svg_icon/add-icon.svg";
import UserIcon from "../../assets/images/svg_icon/user-icon.svg";
import LikeIcon from "../../assets/images/svg_icon/like-icon-new.svg";
import CommentIcon from "../../assets/images/svg_icon/coment.svg";
import TimeIcon from "../../assets/images/svg_icon/schedulepost.svg";
import LcationIcon from "../../assets/images/svg_icon/location-svg.svg";
import PlayStore from "../../assets/images/play-sore-button.svg";
import AppStore from "../../assets/images/app-store.svg";
import ReelsIcon from "../../assets/images/svg_icon/reel-icon.svg";
import Delete from "../../assets/images/svg_icon/delete-icon.svg";
import MultipleIcon from "../../assets/images/svg_icon/multiple-img-icon.svg";
import PostIcon from "../../assets/images/svg_icon/post-icon.svg";
import PostListIcon from "../../assets/images/svg_icon/post-list-1.svg";
import FilledLikeIcon from "../../assets/images/svg_icon/Filled-liked-icon.svg";
import ShareIcon from "../../assets/images/svg_icon/share-icon.svg";
import CopyLinkIcon from "../../assets/images/svg_icon/copy-Link-icon.svg";
import side_bar_arrow_icon from "../../assets/images/svg_icon/side_bar_arrow_icon.svg";
import analytics_comment from "../../assets/images/svg_icon/analystics-coments.svg";
import analytics_like from "../../assets/images/svg_icon/analytics-like.svg";
import Tumblr from "../../assets/images/Channels/tumblr.svg";
import Reddit from "../../assets/images/Channels/reddit.svg";
import User from "../../assets/images/svg_icon/user-pop.svg";
import Delete_Pop from "../../assets/images/svg_icon/delete-pop.svg";
import Sign_Out from "../../assets/images/svg_icon/sign-out-icon.svg";
import Flowkar_Text from "../../assets/images/svg_icon/Flowkar.svg";
import Flowkar_LOGO from "../../assets/images/svg_icon/flowkar11.svg";
import Thread from "../../assets/images/svg_icon/thread.svg";
import Feed from "../../assets/images/svg_icon/feed-icon.svg";
import Planner from "../../assets/images/Navbar/planner.svg";
import Planner_Active from "../../assets/images/Navbar/planner_active.svg";
import Messages from "../../assets/images/Navbar/messages.svg";
import Messages_Active from "../../assets/images/Navbar/messages_active.svg";
import Analytics from "../../assets/images/Navbar/analytics.svg";
import Analytics_Active from "../../assets/images/Navbar/analytics_active.svg";
import Brands from "../../assets/images/Navbar/brands.svg";
import Feedback from "../../assets/images/svg_icon/feedback.svg";
import UserManagement from "../../assets/images/svg_icon/user_management.svg";
import brandManagement from "../../assets/images/svg_icon/brandManagement.svg";
import flowkar_f from "../../assets/images/Chating/flowkarChat.svg";
import LiveStream from "../../assets/images/svg_icon/liveStream.svg";
import Telegram from "../../assets/images/Analytics/telegram.svg";
import Mastodon from "../../assets/images/Analytics/mastodon.svg";

const SOCIAL_ICONS = {
  INSTAGRAM_ICON: Instagram,
  LINKEDIN_ICON: Linkedin,
  TELEGRAM_ICON: Telegram,
  MASTODON_ICON: Mastodon,
  PINTEREST_ICON: Pinterest,
  FACEBOOK_ICON: Facebook,
  TIKTOK_ICON: TikTok,
  TWITTER_ICON: Twitter,
  VIMEO_ICON: Vimeo,
  YOUTUBE_ICON: YouTube,
  AUDIANCE_ICON: Audience,
  CALENDER_ICON: Calender,
  LOCATION_ICON: Location,
  PERSON_ICON: Person,
  DAILYMOTION_ICON: Dailymation,
  FLOWKARLOGO: logo,
  DUMMY_PROFILE: dummyProfile,
  DELETE_POST_CONFIRMATION: deletePostConfirmation,
  DELETE_USER_INFO: deleteUserInfo,
  DELETE_USER_BG: deleteUserBGImage,
  LIKE_ICON: LikeIcon,
  COMMENT_ICON: CommentIcon,
  LOCATION_ICONN: LcationIcon,
  PLAY_STORE: PlayStore,
  APP_STORE: AppStore,
  REELS_ICON: ReelsIcon,
  DELETE: Delete,
  FEEDBACK: Feedback,
  USER_MANAGEMENT: UserManagement,
  BRAND_MANAGEMENT: brandManagement,
  MULTIPLE_IMG_ICON: MultipleIcon,
  POST_ICON: PostIcon,
  POST_LIST_ICON: PostListIcon,
  FILLED_LIKE_ICON: FilledLikeIcon,
  SHARE_ICON: ShareIcon,
  COPY_LINK_ICON: CopyLinkIcon,
  SIDE_BAR_ARROW_ICON: side_bar_arrow_icon,
  ANALYTICS_LIKE: analytics_like,
  ANALYTICS_COMMENT: analytics_comment,
  TUMBLR_ICON: Tumblr,
  REDDIT_ICON: Reddit,
  USER: User,
  DELETE: Delete_Pop,
  SIGN_OUT: Sign_Out,
  FLOWKAR_TEXT: Flowkar_Text,
  Flowkar_LOGO: Flowkar_LOGO,
  THREADS_ICON: Thread,
  FLOWKAR_F: flowkar_f,
  TELEGRAM: Telegram,
  MASTODON: Mastodon,
};
const ICONS = {
  Dashboard_Icon: DashboardIcon,
  Dashboard_Icon_Active: DashboardIconActive,
  Add_Icon: AddIcon,
  User_Icon: UserIcon,
  Time_Icon: TimeIcon,
  Live_Icon: LiveStream,
  Feed_Icon: Feed,
  Planner: Planner,
  Planner_Active: Planner_Active,
  Messages: Messages,
  Messages_Active: Messages_Active,
  Analytics: Analytics,
  Analytics_Active: Analytics_Active,
  Brands: Brands,
};
const CHANNEL_LIST = [
  {
    id: 1,
    name: "Instagram",
    icon: Instagram,
    iconColor: "#E1306C",
    selected: false,
    color: "bg-pink-100",
  },
  {
    id: 2,
    name: "Facebook",
    icon: Facebook,
    iconColor: "#3b5998",
    selected: false,
    color: "bg-blue-100",
  },
  {
    id: 3,
    name: "YouTube",
    icon: YouTube,
    iconColor: "#FF0000",
    selected: false,
    color: "bg-red-100",
  },
  {
    id: 4,
    name: "Pinterest",
    icon: Pinterest,
    iconColor: "#E60023",
    selected: false,
    color: "bg-[#FFD6D6]",
  },
  {
    id: 5,
    name: "LinkedIn",
    icon: Linkedin,
    iconColor: "#0077b5",
    selected: false,
    color: "bg-[#D1E8FF]",
  },
  // {
  //   id: 6,
  //   name: "Twitter",
  //   icon: Twitter,
  //   iconColor: "#000000",
  //   selected: false,
  //   color: "bg-gray-200",
  // },
  {
    id: 6,
    name: "Vimeo",
    icon: Vimeo,
    iconColor: "#1ab7ea",
    selected: false,
    color: "bg-[#C9F1FF]",
  },

  {
    id: 7,
    name: "Tumblr",
    icon: Tumblr,
    iconColor: "#1ab7ea",
    selected: false,
    color: "bg-[#D6E6FF]",
  },

  {
    id: 8,
    name: "Reddit",
    icon: Reddit,
    iconColor: "#1ab7ea",
    selected: false,
    color: "bg-[#FFD3C3]",
  },
  {
    id: 9,
    name: "Tiktok",
    icon: TikTok,
    iconColor: "#000000",
    selected: false,
    color: "bg-gray-100",
  },
  {
    id: 10,
    name: "Threads",
    icon: Thread,
    iconColor: "#000000",
    selected: false,
    color: "bg-gray-300",
  },
  // {
  //   id: 11,
  //   name: "threads",
  //   icon: Thread,
  //   iconColor: "#000000",
  //   selected: false,
  //   color: "bg-gray-300",
  // },
  {
    id: 12,
    name: "x",
    icon: Twitter,
    iconColor: "#000000",
    selected: false,
    color: "bg-gray-200",
  },
  {
    id: 13,
    name: "Telegram",
    icon: Telegram,
    iconColor: "#0088cc",
    selected: false,
    color: "bg-gray-200",
  },
  {
    id: 14,
    name: "Mastodon",
    icon: Mastodon,
    iconColor: "#6364ffff",
    selected: false,
    color: "bg-gray-200",
  },
  {
    id: 15,
    name: "Flowkar",
    icon: flowkar_f,
    iconColor: "#563D39",
    selected: false,
    color: "bg-[#F5F3F2]",
  },
];

const PLATFORM_FILE_TYPE_SUPPORT = {
  Instagram: ["image", "video"],
  Facebook: ["image", "video"],
  YouTube: ["video"],
  Pinterest: ["image", "video"],
  LinkedIn: ["image", "video"],
  Vimeo: ["video"],
  Tumblr: ["image", "video"],
  Reddit: ["image", "video"],
  Tiktok: ["image", "video"],
  Threads: ["image", "video"],
  x: ["image", "video"],
  Mastodon: ["image", "video"],
  Flowkar: ["image", "video"],
  flowkar: ["image", "video"],
};

const INDENTIFIERS = {
  USERDATA: "userData",
  TOKEN: "token",
  acceptlanguage: "acceptlanguage",
  PLAYER_ID: "playerId",
  currentTab: "currentactivetab",
};

export default {
  SOCIAL_ICONS,
  CHANNEL_LIST,
  INDENTIFIERS,
  ICONS,
  PLATFORM_FILE_TYPE_SUPPORT,
};
